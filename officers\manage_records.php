<?php
require_once '../config/database.php';
startSecureSession();
requireRole(['officer']);

$message = '';
$error_message = '';
$search_term = '';

// Handle record actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = getDBConnection();
        
        if ($action == 'update_status' && isset($_POST['record_id']) && isset($_POST['new_status'])) {
            $record_id = (int)$_POST['record_id'];
            $new_status = $_POST['new_status'];
            
            // Check if this officer owns the record
            $stmt = $pdo->prepare("SELECT case_number FROM criminal_records WHERE record_id = ? AND created_by = ?");
            $stmt->execute([$record_id, $_SESSION['user_id']]);
            $record = $stmt->fetch();
            
            if ($record) {
                $stmt = $pdo->prepare("UPDATE criminal_records SET case_status = ?, updated_at = CURRENT_TIMESTAMP WHERE record_id = ?");
                $stmt->execute([$new_status, $record_id]);
                
                // Add case update
                $stmt = $pdo->prepare("INSERT INTO case_updates (case_number, update_type, update_description, updated_by) 
                                      VALUES (?, 'status_change', ?, ?)");
                $stmt->execute([$record['case_number'], "Status changed to: $new_status", $_SESSION['user_id']]);
                
                $message = "Case status updated successfully.";
            } else {
                $error_message = "You can only update your own cases.";
            }
        }
        
    } catch (PDOException $e) {
        $error_message = "Operation failed. Please try again.";
    }
}

// Handle search
if (isset($_GET['search'])) {
    $search_term = sanitizeInput($_GET['search']);
}

// Get criminal records for this officer
try {
    $pdo = getDBConnection();
    
    if (!empty($search_term)) {
        $stmt = $pdo->prepare("SELECT cr.*, u.first_name, u.last_name
            FROM criminal_records cr 
            LEFT JOIN users u ON cr.created_by = u.user_id 
            WHERE cr.created_by = ? AND (cr.case_number LIKE ? OR cr.suspect_name LIKE ? OR cr.crime_type LIKE ?)
            ORDER BY cr.created_at DESC");
        $search_param = "%$search_term%";
        $stmt->execute([$_SESSION['user_id'], $search_param, $search_param, $search_param]);
    } else {
        $stmt = $pdo->prepare("SELECT cr.*, u.first_name, u.last_name
            FROM criminal_records cr 
            LEFT JOIN users u ON cr.created_by = u.user_id 
            WHERE cr.created_by = ?
            ORDER BY cr.created_at DESC");
        $stmt->execute([$_SESSION['user_id']]);
    }
    
    $records = $stmt->fetchAll();
} catch (PDOException $e) {
    $error_message = "Failed to load criminal records.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>My Cases - CRMS Officer</title>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - My Cases</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Officer <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <!-- Messages -->
        <?php if (!empty($message)): ?>
            <div class="bg-green-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- Search and Actions -->
        <div class="mb-6 flex flex-wrap gap-4 items-center justify-between">
            <div class="flex gap-4">
                <a href="new_record.php" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    File New Record
                </a>
                <a href="dashboard.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Back to Dashboard
                </a>
            </div>
            
            <form method="GET" class="flex gap-2">
                <input type="text" name="search" placeholder="Search my cases..." 
                       value="<?php echo htmlspecialchars($search_term); ?>"
                       class="px-4 py-2 bg-gray-700 text-white border border-gray-600 rounded focus:border-red-500 focus:outline-none w-64">
                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Search</button>
                <?php if (!empty($search_term)): ?>
                    <a href="manage_records.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">Clear</a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Records Table -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
            <h2 class="text-white text-xl font-bold mb-4">
                My Criminal Records 
                <?php if (!empty($search_term)): ?>
                    <span class="text-sm text-gray-400">(Search results for: "<?php echo htmlspecialchars($search_term); ?>")</span>
                <?php endif; ?>
            </h2>
            
            <div class="overflow-x-auto">
                <table class="w-full text-white text-sm">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-3">Case Number</th>
                            <th class="text-left py-3">Suspect</th>
                            <th class="text-left py-3">Crime Type</th>
                            <th class="text-left py-3">Status</th>
                            <th class="text-left py-3">Incident Date</th>
                            <th class="text-left py-3">Last Updated</th>
                            <th class="text-left py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($records)): ?>
                            <?php foreach ($records as $record): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-700">
                                    <td class="py-3 font-mono"><?php echo htmlspecialchars($record['case_number']); ?></td>
                                    <td class="py-3"><?php echo htmlspecialchars($record['suspect_name']); ?></td>
                                    <td class="py-3"><?php echo htmlspecialchars($record['crime_type']); ?></td>
                                    <td class="py-3">
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="update_status">
                                            <input type="hidden" name="record_id" value="<?php echo $record['record_id']; ?>">
                                            <select name="new_status" onchange="this.form.submit()" 
                                                    class="px-2 py-1 rounded text-xs bg-gray-600 border border-gray-500
                                                    <?php 
                                                    switch($record['case_status']) {
                                                        case 'solved': echo 'text-green-300'; break;
                                                        case 'ongoing': echo 'text-yellow-300'; break;
                                                        case 'paused': echo 'text-blue-300'; break;
                                                        case 'unresolved': echo 'text-red-300'; break;
                                                        default: echo 'text-gray-300';
                                                    }
                                                    ?>">
                                                <option value="ongoing" <?php echo $record['case_status'] == 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                                                <option value="solved" <?php echo $record['case_status'] == 'solved' ? 'selected' : ''; ?>>Solved</option>
                                                <option value="paused" <?php echo $record['case_status'] == 'paused' ? 'selected' : ''; ?>>Paused</option>
                                                <option value="unresolved" <?php echo $record['case_status'] == 'unresolved' ? 'selected' : ''; ?>>Unresolved</option>
                                            </select>
                                        </form>
                                    </td>
                                    <td class="py-3"><?php echo date('M d, Y', strtotime($record['incident_date'])); ?></td>
                                    <td class="py-3"><?php echo date('M d, Y H:i', strtotime($record['updated_at'])); ?></td>
                                    <td class="py-3">
                                        <div class="flex space-x-2">
                                            <a href="view_record.php?id=<?php echo $record['record_id']; ?>" 
                                               class="text-blue-400 hover:text-blue-300 text-xs">View</a>
                                            <a href="edit_record.php?id=<?php echo $record['record_id']; ?>" 
                                               class="text-green-400 hover:text-green-300 text-xs">Edit</a>
                                            <a href="case_updates.php?case=<?php echo urlencode($record['case_number']); ?>" 
                                               class="text-purple-400 hover:text-purple-300 text-xs">Updates</a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-8 text-gray-400">
                                    <?php echo !empty($search_term) ? 'No records found matching your search.' : 'You have not filed any criminal records yet.'; ?>
                                    <?php if (empty($search_term)): ?>
                                        <br><a href="new_record.php" class="text-blue-400 hover:text-blue-300 underline">File your first record</a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-yellow-400 font-semibold">Ongoing</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'ongoing'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-green-400 font-semibold">Solved</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'solved'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-blue-400 font-semibold">Paused</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'paused'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-red-400 font-semibold">Unresolved</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($records, function($r) { return $r['case_status'] == 'unresolved'; })); ?>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
