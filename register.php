<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Register - CRMS</title>
    <style>
        .form-container {
            width: 500px;
            height: auto;
            border: 2px solid rgb(225, 3, 3);
            box-shadow: 0px 0px 9px rgb(225, 3, 3);
            border-radius: 4px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            padding: 20px;
        }

        .registration-form {
            display: flex;
            justify-content: center;
            flex-direction: column;
            gap: 15px;
        }

        .heading {
            color: white;
            font-size: 30px;
            text-align: center;
            margin-bottom: 25px;
        }

        .center {
            margin-top: 60px;
            margin-left: auto;
            margin-right: auto;
        }

        .form-control {
            width: 100%;
            height: 40px;
            border: 2px dashed rgb(225, 3, 3);
        }

        .submit-button {
            background-color: #dd0000;
            color: white;
            font-size: 15px;
            width: 200px;
            height: 35px;
            border-radius: 5px;
            align-content: center;
            margin-top: 20px;
            margin-left: auto;
            margin-right: auto;
        }

        .submit-button:hover {
            background-color: #dd0000;
            opacity: 0.8;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d]">
    <?php
    require_once 'config/database.php';
    startSecureSession();
    
    $error_message = '';
    $success_message = '';
    
    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        $first_name = sanitizeInput($_POST['first_name']);
        $last_name = sanitizeInput($_POST['last_name']);
        $email = sanitizeInput($_POST['email']);
        $phone = sanitizeInput($_POST['phone']);
        $address = sanitizeInput($_POST['address']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        
        // Validation
        if (empty($first_name) || empty($last_name) || empty($email) || empty($password)) {
            $error_message = "Please fill in all required fields.";
        } elseif ($password !== $confirm_password) {
            $error_message = "Passwords do not match.";
        } elseif (strlen($password) < 6) {
            $error_message = "Password must be at least 6 characters long.";
        } else {
            try {
                $pdo = getDBConnection();
                
                // Check if email already exists
                $stmt = $pdo->prepare("SELECT user_id FROM users WHERE email = ?");
                $stmt->execute([$email]);
                if ($stmt->fetch()) {
                    $error_message = "Email address already registered.";
                } else {
                    // Insert new citizen
                    $password_hash = generateSecureHash($password);
                    $stmt = $pdo->prepare("
                        INSERT INTO users (first_name, last_name, email, password_hash, role, phone, address) 
                        VALUES (?, ?, ?, ?, 'citizen', ?, ?)
                    ");
                    $stmt->execute([$first_name, $last_name, $email, $password_hash, $phone, $address]);
                    
                    $success_message = "Registration successful! You can now login.";
                }
            } catch (PDOException $e) {
                $error_message = "Registration failed. Please try again.";
            }
        }
    }
    ?>

    <div class="center">
        <div class="form-container">
            <p class="heading">Citizen Registration</p>
            
            <?php if (!empty($error_message)): ?>
                <div class="text-red-500 text-center mb-4 text-sm">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
                <div class="text-green-500 text-center mb-4 text-sm">
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>

            <form class="registration-form" method="POST" action="">
                <div class="form-control">
                    <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                           type="text" placeholder="First Name *" name="first_name" required />
                </div>

                <div class="form-control">
                    <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                           type="text" placeholder="Last Name *" name="last_name" required />
                </div>

                <div class="form-control">
                    <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                           type="email" placeholder="Email Address *" name="email" required />
                </div>

                <div class="form-control">
                    <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                           type="tel" placeholder="Phone Number" name="phone" />
                </div>

                <div class="form-control">
                    <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                           type="text" placeholder="Address" name="address" />
                </div>

                <div class="form-control">
                    <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                           type="password" placeholder="Password *" name="password" required />
                </div>

                <div class="form-control">
                    <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                           type="password" placeholder="Confirm Password *" name="confirm_password" required />
                </div>

                <button class="submit-button" type="submit">Register</button>

                <div class="text-center mt-4">
                    <p class="text-white text-sm">Already have an account?</p>
                    <a href="login.php" class="text-red-500 text-sm underline hover:text-red-400">Login here</a>
                </div>
            </form>
        </div>
    </div>

    <?php include "back.php"; ?>
</body>
</html>
