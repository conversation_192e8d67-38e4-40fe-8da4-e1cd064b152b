# Criminal Record Management System (CRMS)
## Lesotho Police Force

A comprehensive web-based Criminal Record Management System designed for the Lesotho Police Force to manage criminal records, complaints, and case tracking efficiently.

## 🚀 Features

### Administrator Features (30 marks)
- ✅ **Login System** - Secure authentication for administrators
- ✅ **Dashboard Analytics** - Real-time statistics on cases, crimes, resolved/unresolved/paused cases
- ✅ **Officer Management** - Register new police officers and manage their roles
- ✅ **User Management** - Display, edit, and delete user information for officers and citizens
- ✅ **Criminal Records Management** - Full CRUD operations with search by case number
- ✅ **Report Generation** - Generate and download comprehensive reports on cases

### Police Officer Features (18 marks)
- ✅ **Login & Dashboard** - Secure access to officer-specific dashboard
- ✅ **Analytics Dashboard** - Personal statistics on ongoing, solved, and unresolved cases
- ✅ **Criminal Records Management** - File new records, update case details, and manage case status
- ✅ **Report Generation** - Generate and download reports on personal cases

### Citizen Features (10 marks)
- ✅ **Login System** - Secure citizen portal access
- ✅ **Dashboard** - Track case progress (ongoing, resolved, paused)
- ✅ **Complaint Management** - Register complaints, view status, and track attendance

### Frontend Features (17 marks)
- ✅ **Role-specific Forms** - Tailored forms for each user role and task
- ✅ **Data Tables** - Professional tables for displaying and managing information
- ✅ **Navigation & Footer** - Consistent navigation bars and footers with styling
- ✅ **Interactive Buttons** - Well-designed buttons with hover effects and animations
- ✅ **Consistent Design** - Unified color scheme, font styles, and readability
- ✅ **Database Schema** - Comprehensive database design supporting all functionality

## 🛠 Technology Stack

- **Backend**: PHP 8.0+
- **Frontend**: HTML5, Tailwind CSS, JavaScript
- **Database**: MySQL 8.0+
- **Server**: Apache (XAMPP)
- **Security**: PDO prepared statements, password hashing, session management

## 📋 Installation & Setup

### Prerequisites
- XAMPP (Apache + MySQL + PHP)
- Web browser (Chrome, Firefox, Safari, Edge)

### Installation Steps

1. **Download and Install XAMPP**
   - Download from [https://www.apachefriends.org/](https://www.apachefriends.org/)
   - Install and start Apache and MySQL services

2. **Clone/Download Project**
   ```bash
   # Place the project folder in your XAMPP htdocs directory
   # Example: C:\xampp\htdocs\crms
   ```

3. **Database Setup**
   - Open your web browser
   - Navigate to: `http://localhost/your-project-folder/setup.php`
   - Click "Initialize Database" to create all tables and default admin user

4. **Access the System**
   - Home Page: `http://localhost/your-project-folder/index.php`
   - Login Page: `http://localhost/your-project-folder/login.php`

### Default Admin Credentials
```
Email: <EMAIL>
Password: admin123
```
**⚠️ Change these credentials immediately after first login**

## 📊 Database Schema

### Core Tables
- **users** - System users (admin, officers, citizens)
- **criminal_records** - Criminal case records
- **complaints** - Citizen complaints
- **case_updates** - Case update history
- **hearings** - Court hearing schedules

### Key Relationships
- Users → Criminal Records (created_by, arresting_officer_id)
- Users → Complaints (citizen_id, assigned_officer_id)
- Criminal Records → Case Updates (case_number)

## 🔐 Security Features

- **Password Hashing** - Secure password storage using PHP password_hash()
- **SQL Injection Prevention** - PDO prepared statements
- **XSS Protection** - Input sanitization and output escaping
- **Role-based Access Control** - Strict permission checking
- **Session Management** - Secure session handling

## 👥 User Roles & Permissions

### Administrator
- Full system access
- User management
- All criminal records
- System reports
- Officer registration

### Police Officer
- Personal dashboard
- Create/manage own cases
- Update case status
- Generate personal reports
- View assigned cases

### Citizen
- Personal dashboard
- File complaints
- Track complaint status
- View case progress
- Update personal profile

## 📱 Responsive Design

The system is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- Various screen sizes

## 🎨 Design Features

- **Modern UI** - Clean, professional interface
- **Dark Theme** - Easy on the eyes with red accent colors
- **Animations** - Smooth transitions and hover effects
- **Consistent Branding** - Lesotho Police Force theme throughout
- **Accessibility** - Clear typography and color contrast

## 📈 Reporting Features

### Admin Reports
- Cases summary by status and type
- Monthly statistics
- Officer performance analysis
- Crime types analysis

### Officer Reports
- Personal case reports
- Performance summaries
- Monthly case summaries

## 🔧 Configuration

### Database Configuration
Edit `config/database.php` to modify:
- Database host
- Database name
- Username/password
- Connection settings

### System Settings
- Session timeout
- Password requirements
- File upload limits
- Email settings

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure MySQL is running in XAMPP
   - Check database credentials in config/database.php
   - Run setup.php to initialize database

2. **Login Issues**
   - Verify user exists in database
   - Check password is correct
   - Clear browser cache and cookies

3. **Permission Errors**
   - Ensure proper file permissions
   - Check user role assignments
   - Verify session is active

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Emergency**: 911
- **Non-Emergency**: +266 2231-2345

## 📄 License

© 2025 Lesotho Police Force. All rights reserved.

## 🤝 Contributing

This system was developed for the Lesotho Police Force. For modifications or enhancements, please contact the system administrator.

---

**Developed for**: Lesotho Police Force  
**Project**: Criminal Record Management System  
**Version**: 1.0  
**Last Updated**: January 2025
