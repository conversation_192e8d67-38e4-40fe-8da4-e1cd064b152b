
<!DOCTYPE html>
<html>
    <head>
      <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
        <title>Login to CRMS</title>
        <style>
             .form-container{
              width:450px;
              height:550px;
              border:2px solid rgb(225, 3, 3);
              box-shadow: 0px 0px 9px  rgb(225, 3, 3) ;
                 border-radius:4px;
               display:flex;
               justify-content: center;
               flex-direction: column;
                
             
            } 

            .registration-form{
               display:flex;
               justify-content: center;
               flex-direction: column;
               gap:15px;
               
            }

            .heading{
              color:white;
              font-size:30px;
              text-align:center;
              margin-bottom:25px;
            }

            .center{
            margin-top:60px;
            margin-left:170px;
            }

            .form-control{
              width:350px;
              height:40px;
              border:2px dashed rgb(225, 3, 3);
              margin-left:47px

            }

            .submit-button{
              background-color: #dd0000;
              color:white;
              font-size:15px;
              width:200px;
              height:35px;
              border-radius:5px;
              align-content:center;
              margin-top:20px
            }

            .submit-button:hover{
              background-color:#dd0000;
              opacity:0.8;
              
            }
           
            /* .center-warning{
              display:flex;
              justify-content: center;
              flex-direction:column
            } */

            .warning{
              text-align:center;
               margin-top:10px;
            }

            .center-warning{
              
              width:30%;
              height:50%;
               margin-right:15%;
               margin-top:10%
            }

            .all{
              display:flex;
              justify-content: space-between;
              flex-direction:row;
              margin-top:2%
              
            }
            
            </style>
    </head>
    <body class="bg-gradient-to-l from-[#000000] via-[#121417]  to-[#25282d]">
      <div class="all">
      <div class="center">
     <div class="form-container">
       <p class="heading">Login</p>
      <form id="form" class="registration-form" method="POST" action=""> 
          <div class="form-control">
           <!-- <p class="text-white">First Name</p> -->
          <input  class="text-white pt-1.5 pl-2" type="text" id="first_name" placeholder="Enter first name" name="first_name" required />
          
        </div>

        <div class="form-control">
          <!-- <p>Last Name</p> -->
          <input  class="text-white pt-1.5 pl-2" type="text" id="last_name" placeholder="Enter last name" name="last_name" required />
         
        </div>

        <div class="form-control">
        <!-- <p>Email</p> -->
          <input class="text-white pt-1.5 pl-2" type="text" id="email" placeholder="Enter email" name="email" required />
          
        </div>

        <div class="form-control">
        <!-- <p>Services</p>  -->
           <input  class="text-white pt-1.5 pl-2" type="text" id="service" placeholder="Enter service" name="service" required/>
           
        </div>
        <div class="form-control">
          <!-- <p>User password</p> -->
          <input  class="text-white pt-1.5 pl-2" type="password" id="password" placeholder="Enter password" name="password" required />
          
        </div>
        <!-- <div class="button-wrapper"> -->
          <center><button class="submit-button" type="submit">Submit</button></center>
        <!-- </div> -->
      </form>
     </div>
</div>
    

    <div class="center-warning">
     <div class="warning">
     <p class="text-3xl text-red-500 mb-5">
      CAUTION!
     </p>
     <p class="text-xl text-white mb-8">
      The users of this system are known by administrators and recorded in a secure database for quick authorization.
      Any unknown user or suspicious activity is captured almost instantly, and 2 more entry chances are given.
      If none succeed, the entries will be deemed malicious and the account/user/entries will be blocked.
     </p>
     <p class="text-sm text-red-500">To recover your account, visit the Lesotho Police Force Headquarters, or<br> <a class="underline" href="contact.php">go to contact page</a> to contact in-office workers</p>

     <?php
       include "back.php";
     ?>
     </div>
    </div>


      </div>
</html>