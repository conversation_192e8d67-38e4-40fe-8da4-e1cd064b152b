<?php
     session_start();
     require_once 'config/database.php';
       

       $error_message = '';
       $success_message = '';

       if ($_SERVER['REQUEST_METHOD'] == 'POST') {
           $email = sanitizeInput($_POST['email']);
           $password = $_POST['password'];

           if (empty($email) || empty($password)) {
               $error_message = "Please fill in all required fields.";
           } else {
               try {
                   $pdo = getDBConnection();
                   $stmt = $pdo->prepare("SELECT user_id, first_name, last_name, email, password_hash, role, status FROM users WHERE email = ? AND status = 'active'");
                   $stmt->execute([$email]);
                   $user = $stmt->fetch();

                   if ($user && verifyPassword($password, $user['password_hash'])) {
                       $_SESSION['user_id'] = $user['user_id'];
                       $_SESSION['user_role'] = $user['role'];
                       $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
                       $_SESSION['user_email'] = $user['email'];

                       // Redirect based on role
                       switch ($user['role']) {
                           case 'admin':
                               header('Location: admin/dashboard.php');
                               break;
                           case 'officer':
                               header('Location: officers/dashboard.php');
                               break;
                           case 'citizen':
                               header('Location: citizens/dashboard.php');
                               break;
                           default:
                               $error_message = "Invalid user role.";
                       }
                       exit();
                   } else {
                       $error_message = "Invalid email or password.";
                   }
               } catch (PDOException $e) {
                   $error_message = "Login failed. Please try again.";
               }
           }
       }

       include "back.php";
     ?>
<!DOCTYPE html>
<html>
    <head>
      <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
        <title>Login to CRMS</title>
        <style>
             .form-container{
              width:450px;
              height:550px;
              border:2px solid rgb(225, 3, 3);
              box-shadow: 0px 0px 9px  rgb(225, 3, 3) ;
                 border-radius:4px;
               display:flex;
               justify-content: center;
               flex-direction: column;
                
             
            } 

            .registration-form{
               display:flex;
               justify-content: center;
               flex-direction: column;
               gap:15px;
               
            }

            .heading{
              color:white;
              font-size:30px;
              text-align:center;
              margin-bottom:25px;
            }

            .center{
            margin-top:60px;
            margin-left:170px;
            }

            .form-control{
              width:350px;
              height:40px;
              border:2px dashed rgb(225, 3, 3);
              margin-left:47px

            }

            .submit-button{
              background-color: #dd0000;
              color:white;
              font-size:15px;
              width:200px;
              height:35px;
              border-radius:5px;
              align-content:center;
              margin-top:20px
            }

            .submit-button:hover{
              background-color:#dd0000;
              opacity:0.8;
              
            }
           
            /* .center-warning{
              display:flex;
              justify-content: center;
              flex-direction:column
            } */

            .warning{
              text-align:center;
               margin-top:10px;
            }

            .center-warning{
              
              width:30%;
              height:50%;
               margin-right:15%;
               margin-top:10%
            }

            .all{
              display:flex;
              justify-content: space-between;
              flex-direction:row;
              margin-top:2%
              
            }
            
            </style>
    </head>
    <body class="bg-gradient-to-l from-[#000000] via-[#121417]  to-[#25282d]">
      <div class="all">
      <div class="center">
     <div class="form-container">
       <p class="heading">Login</p>
      <?php if (!empty($error_message)): ?>
        <div class="text-red-500 text-center mb-4 text-sm">
          <?php echo htmlspecialchars($error_message); ?>
        </div>
      <?php endif; ?>

      <form id="form" class="registration-form" method="POST" action="">
        <div class="form-control">
          <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                 type="email" id="email" placeholder="Enter email address" name="email" required />
        </div>

        <div class="form-control">
          <input class="text-white pt-1.5 pl-2 w-full h-full bg-transparent border-0 outline-0"
                 type="password" id="password" placeholder="Enter password" name="password" required />
        </div>

        <center><button class="submit-button" type="submit">Login</button></center>

        <div class="text-center mt-4">
          <p class="text-white text-sm">Don't have an account?</p>
          <a href="register.php" class="text-red-500 text-sm underline hover:text-red-400">Register here</a>
        </div>
      </form>
     </div>
</div>
    

    <div class="center-warning">
     <div class="warning">
     <p class="text-3xl text-red-500 mb-5">
      CAUTION!
     </p>
     <p class="text-xl text-white mb-8">
      The users of this system are known by administrators and recorded in a secure database for quick authorization.
      Any unknown user or suspicious activity is captured almost instantly, and 2 more entry chances are given.
      If none succeed, the entries will be deemed malicious and the account/user/entries will be blocked.
     </p>
     <p class="text-sm text-red-500">To recover your account, visit the Lesotho Police Force Headquarters, or<br> <a class="underline" href="contact.php">go to contact page</a> to contact in-office workers</p>

     
     </div>
    </div>


      </div>
</html>