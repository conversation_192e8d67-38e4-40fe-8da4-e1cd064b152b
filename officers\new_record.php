<?php
require_once '../config/database.php';
startSecureSession();
requireRole(['officer']);

$message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $suspect_name = sanitizeInput($_POST['suspect_name']);
    $suspect_id_number = sanitizeInput($_POST['suspect_id_number']);
    $crime_type = sanitizeInput($_POST['crime_type']);
    $crime_description = sanitizeInput($_POST['crime_description']);
    $incident_date = $_POST['incident_date'];
    $incident_location = sanitizeInput($_POST['incident_location']);
    $evidence = sanitizeInput($_POST['evidence']);
    $witness_info = sanitizeInput($_POST['witness_info']);
    $case_status = $_POST['case_status'];
    
    // Validation
    if (empty($suspect_name) || empty($crime_type) || empty($crime_description) || empty($incident_date) || empty($incident_location)) {
        $error_message = "Please fill in all required fields.";
    } else {
        try {
            $pdo = getDBConnection();
            
            // Generate unique case number
            $case_number = 'CASE-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            
            // Check if case number exists
            $stmt = $pdo->prepare("SELECT case_number FROM criminal_records WHERE case_number = ?");
            $stmt->execute([$case_number]);
            while ($stmt->fetch()) {
                $case_number = 'CASE-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
                $stmt->execute([$case_number]);
            }
            
            // Insert new criminal record
            $stmt = $pdo->prepare("
                INSERT INTO criminal_records (
                    case_number, suspect_name, suspect_id_number, crime_type, crime_description,
                    incident_date, incident_location, arresting_officer_id, case_status,
                    evidence, witness_info, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $case_number, $suspect_name, $suspect_id_number, $crime_type, $crime_description,
                $incident_date, $incident_location, $_SESSION['user_id'], $case_status,
                $evidence, $witness_info, $_SESSION['user_id']
            ]);
            
            // Add initial case update
            $stmt = $pdo->prepare("
                INSERT INTO case_updates (case_number, update_type, update_description, updated_by) 
                VALUES (?, 'general_update', 'Criminal record created', ?)
            ");
            $stmt->execute([$case_number, $_SESSION['user_id']]);
            
            $message = "Criminal record created successfully! Case Number: " . $case_number;
            
            // Clear form data
            $suspect_name = $suspect_id_number = $crime_type = $crime_description = '';
            $incident_date = $incident_location = $evidence = $witness_info = '';
            $case_status = 'ongoing';
            
        } catch (PDOException $e) {
            $error_message = "Failed to create criminal record. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>File New Criminal Record - CRMS</title>
    <style>
        .form-container {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 30px;
        }
        .form-control {
            width: 100%;
            border: 2px solid #dc2626;
            border-radius: 5px;
            background: transparent;
            color: white;
            padding: 10px 15px;
        }
        .form-control:focus {
            outline: none;
            border-color: #ef4444;
            box-shadow: 0 0 10px rgba(220, 38, 38, 0.3);
        }
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - File New Criminal Record</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <a href="manage_records.php" class="text-white hover:text-gray-300">My Cases</a>
                <span class="text-white">Officer <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Messages -->
            <?php if (!empty($message)): ?>
                <div class="bg-green-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
                <div class="bg-red-600 text-white p-4 rounded mb-6">
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>

            <div class="form-container">
                <h2 class="text-white text-2xl font-bold text-center mb-6">File New Criminal Record</h2>
                
                <form method="POST" action="" class="space-y-6">
                    <!-- Suspect Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Suspect Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Suspect Name *</label>
                                <input type="text" name="suspect_name" class="form-control" 
                                       placeholder="Enter suspect's full name" 
                                       value="<?php echo htmlspecialchars($suspect_name ?? ''); ?>" required>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">ID Number</label>
                                <input type="text" name="suspect_id_number" class="form-control" 
                                       placeholder="Enter ID number (if known)" 
                                       value="<?php echo htmlspecialchars($suspect_id_number ?? ''); ?>">
                            </div>
                        </div>
                    </div>

                    <!-- Crime Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Crime Information</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Crime Type *</label>
                                <select name="crime_type" class="form-control" required>
                                    <option value="">Select Crime Type</option>
                                    <option value="Theft">Theft</option>
                                    <option value="Burglary">Burglary</option>
                                    <option value="Assault">Assault</option>
                                    <option value="Fraud">Fraud</option>
                                    <option value="Drug Offense">Drug Offense</option>
                                    <option value="Traffic Violation">Traffic Violation</option>
                                    <option value="Domestic Violence">Domestic Violence</option>
                                    <option value="Vandalism">Vandalism</option>
                                    <option value="Murder">Murder</option>
                                    <option value="Robbery">Robbery</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Crime Description *</label>
                                <textarea name="crime_description" class="form-control" 
                                          placeholder="Provide detailed description of the crime..."
                                          required><?php echo htmlspecialchars($crime_description ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Incident Details -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Incident Details</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Incident Date *</label>
                                <input type="date" name="incident_date" class="form-control" 
                                       value="<?php echo htmlspecialchars($incident_date ?? ''); ?>" required>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Case Status *</label>
                                <select name="case_status" class="form-control" required>
                                    <option value="ongoing" <?php echo ($case_status ?? 'ongoing') == 'ongoing' ? 'selected' : ''; ?>>Ongoing</option>
                                    <option value="solved" <?php echo ($case_status ?? '') == 'solved' ? 'selected' : ''; ?>>Solved</option>
                                    <option value="paused" <?php echo ($case_status ?? '') == 'paused' ? 'selected' : ''; ?>>Paused</option>
                                    <option value="unresolved" <?php echo ($case_status ?? '') == 'unresolved' ? 'selected' : ''; ?>>Unresolved</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="text-white text-sm font-semibold mb-2 block">Incident Location *</label>
                            <input type="text" name="incident_location" class="form-control" 
                                   placeholder="Enter location where incident occurred" 
                                   value="<?php echo htmlspecialchars($incident_location ?? ''); ?>" required>
                        </div>
                    </div>

                    <!-- Additional Information -->
                    <div class="bg-gray-700 rounded-lg p-4">
                        <h3 class="text-white text-lg font-semibold mb-4">Additional Information</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Evidence</label>
                                <textarea name="evidence" class="form-control" 
                                          placeholder="Describe any evidence collected..."><?php echo htmlspecialchars($evidence ?? ''); ?></textarea>
                            </div>
                            <div>
                                <label class="text-white text-sm font-semibold mb-2 block">Witness Information</label>
                                <textarea name="witness_info" class="form-control" 
                                          placeholder="Provide witness details and statements..."><?php echo htmlspecialchars($witness_info ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="text-center pt-4">
                        <button type="submit" class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-red-700 transition duration-300 mr-4">
                            File Criminal Record
                        </button>
                        <a href="dashboard.php" class="bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-gray-700 transition duration-300">
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
