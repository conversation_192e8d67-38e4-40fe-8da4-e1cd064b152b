<?php
require_once '../config/database.php';
startSecureSession();
requireRole(['citizen']);

// Get citizen's complaints and case progress
try {
    $pdo = getDBConnection();
    
    // Get complaint statistics for this citizen
    $stmt = $pdo->prepare("SELECT 
        COUNT(*) as total_complaints,
        SUM(CASE WHEN status = 'attended' THEN 1 ELSE 0 END) as attended_complaints,
        SUM(CASE WHEN status = 'not_attended' THEN 1 ELSE 0 END) as not_attended_complaints,
        SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_complaints,
        SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_complaints
        FROM complaints WHERE citizen_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $complaint_stats = $stmt->fetch();
    
    // Get recent complaints
    $stmt = $pdo->prepare("SELECT c.*, u.first_name, u.last_name 
        FROM complaints c 
        LEFT JOIN users u ON c.assigned_officer_id = u.user_id 
        WHERE c.citizen_id = ? ORDER BY c.created_at DESC LIMIT 5");
    $stmt->execute([$_SESSION['user_id']]);
    $recent_complaints = $stmt->fetchAll();
    
    // Get case progress (cases where citizen might be involved)
    $stmt = $pdo->query("SELECT 
        COUNT(*) as total_cases,
        SUM(CASE WHEN case_status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_cases,
        SUM(CASE WHEN case_status = 'solved' THEN 1 ELSE 0 END) as resolved_cases,
        SUM(CASE WHEN case_status = 'paused' THEN 1 ELSE 0 END) as paused_cases
        FROM criminal_records");
    $case_stats = $stmt->fetch();
    
} catch (PDOException $e) {
    $error_message = "Failed to load dashboard data.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>Citizen Dashboard - CRMS</title>
    <style>
        .stat-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(220, 38, 38, 0.3);
        }
        .nav-link {
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }
        .nav-link:hover {
            background-color: #dc2626;
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - Citizen Portal</h1>
            </div>
            <div class="flex items-center space-x-4">
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-8">
        <!-- Quick Navigation -->
        <div class="mb-8">
            <div class="flex flex-wrap gap-4">
                <a href="new_complaint.php" class="nav-link bg-green-700">File Complaint</a>
                <a href="my_complaints.php" class="nav-link bg-gray-700">My Complaints</a>
                <a href="case_progress.php" class="nav-link bg-gray-700">Track Cases</a>
                <a href="profile.php" class="nav-link bg-gray-700">My Profile</a>
            </div>
        </div>

        <!-- Complaint Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stat-card">
                <h3 class="text-red-500 text-lg font-semibold mb-2">My Complaints</h3>
                <p class="text-white text-3xl font-bold"><?php echo $complaint_stats['total_complaints'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-green-500 text-lg font-semibold mb-2">Attended</h3>
                <p class="text-white text-3xl font-bold"><?php echo $complaint_stats['attended_complaints'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-yellow-500 text-lg font-semibold mb-2">In Progress</h3>
                <p class="text-white text-3xl font-bold"><?php echo $complaint_stats['in_progress_complaints'] ?? 0; ?></p>
            </div>
            <div class="stat-card">
                <h3 class="text-blue-500 text-lg font-semibold mb-2">Resolved</h3>
                <p class="text-white text-3xl font-bold"><?php echo $complaint_stats['resolved_complaints'] ?? 0; ?></p>
            </div>
        </div>

        <!-- Case Progress Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="stat-card">
                <h3 class="text-yellow-500 text-lg font-semibold mb-2">Ongoing Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['ongoing_cases'] ?? 0; ?></p>
                <p class="text-gray-400 text-sm mt-2">System-wide</p>
            </div>
            <div class="stat-card">
                <h3 class="text-green-500 text-lg font-semibold mb-2">Resolved Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['resolved_cases'] ?? 0; ?></p>
                <p class="text-gray-400 text-sm mt-2">System-wide</p>
            </div>
            <div class="stat-card">
                <h3 class="text-blue-500 text-lg font-semibold mb-2">Paused Cases</h3>
                <p class="text-white text-3xl font-bold"><?php echo $case_stats['paused_cases'] ?? 0; ?></p>
                <p class="text-gray-400 text-sm mt-2">System-wide</p>
            </div>
        </div>

        <!-- Recent Complaints -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6 mb-8">
            <h2 class="text-white text-xl font-bold mb-4">My Recent Complaints</h2>
            <div class="overflow-x-auto">
                <table class="w-full text-white">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-2">Complaint #</th>
                            <th class="text-left py-2">Type</th>
                            <th class="text-left py-2">Status</th>
                            <th class="text-left py-2">Assigned Officer</th>
                            <th class="text-left py-2">Date Filed</th>
                            <th class="text-left py-2">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($recent_complaints)): ?>
                            <?php foreach ($recent_complaints as $complaint): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-700">
                                    <td class="py-2"><?php echo htmlspecialchars($complaint['complaint_number']); ?></td>
                                    <td class="py-2"><?php echo htmlspecialchars($complaint['complaint_type']); ?></td>
                                    <td class="py-2">
                                        <span class="px-2 py-1 rounded text-xs
                                            <?php 
                                            switch($complaint['status']) {
                                                case 'resolved': echo 'bg-green-600'; break;
                                                case 'attended': echo 'bg-blue-600'; break;
                                                case 'in_progress': echo 'bg-yellow-600'; break;
                                                case 'not_attended': echo 'bg-red-600'; break;
                                                default: echo 'bg-gray-600';
                                            }
                                            ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $complaint['status'])); ?>
                                        </span>
                                    </td>
                                    <td class="py-2">
                                        <?php 
                                        if ($complaint['first_name']) {
                                            echo htmlspecialchars($complaint['first_name'] . ' ' . $complaint['last_name']);
                                        } else {
                                            echo '<span class="text-gray-400">Not assigned</span>';
                                        }
                                        ?>
                                    </td>
                                    <td class="py-2"><?php echo date('M d, Y', strtotime($complaint['created_at'])); ?></td>
                                    <td class="py-2">
                                        <a href="view_complaint.php?id=<?php echo $complaint['complaint_id']; ?>" 
                                           class="text-blue-400 hover:text-blue-300">View</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="6" class="text-center py-4 text-gray-400">No complaints found</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h3 class="text-white text-lg font-bold mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="new_complaint.php" class="block bg-green-600 text-white text-center py-2 rounded hover:bg-green-700">
                        File New Complaint
                    </a>
                    <a href="my_complaints.php" class="block bg-blue-600 text-white text-center py-2 rounded hover:bg-blue-700">
                        View All My Complaints
                    </a>
                    <a href="case_progress.php" class="block bg-purple-600 text-white text-center py-2 rounded hover:bg-purple-700">
                        Track Case Progress
                    </a>
                </div>
            </div>
            
            <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
                <h3 class="text-white text-lg font-bold mb-4">Important Information</h3>
                <div class="text-gray-300 space-y-2 text-sm">
                    <p><strong>Emergency:</strong> Call 911 for immediate assistance</p>
                    <p><strong>Non-Emergency:</strong> Use this portal for complaints</p>
                    <p><strong>Response Time:</strong> 24-48 hours for complaint review</p>
                    <p><strong>Status Updates:</strong> Check this dashboard regularly</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
