<?php
require_once '../config/database.php';
startSecureSession();
requireRole(['citizen']);

$search_term = '';

// Handle search
if (isset($_GET['search'])) {
    $search_term = sanitizeInput($_GET['search']);
}

// Get citizen's complaints
try {
    $pdo = getDBConnection();
    
    if (!empty($search_term)) {
        $stmt = $pdo->prepare("SELECT c.*, u.first_name, u.last_name 
            FROM complaints c 
            LEFT JOIN users u ON c.assigned_officer_id = u.user_id 
            WHERE c.citizen_id = ? AND (c.complaint_number LIKE ? OR c.complaint_type LIKE ? OR c.complaint_description LIKE ?)
            ORDER BY c.created_at DESC");
        $search_param = "%$search_term%";
        $stmt->execute([$_SESSION['user_id'], $search_param, $search_param, $search_param]);
    } else {
        $stmt = $pdo->prepare("SELECT c.*, u.first_name, u.last_name 
            FROM complaints c 
            LEFT JOIN users u ON c.assigned_officer_id = u.user_id 
            WHERE c.citizen_id = ? 
            ORDER BY c.created_at DESC");
        $stmt->execute([$_SESSION['user_id']]);
    }
    
    $complaints = $stmt->fetchAll();
} catch (PDOException $e) {
    $error_message = "Failed to load complaints.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>My Complaints - CRMS Citizen</title>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - My Complaints</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Welcome, <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <!-- Search and Actions -->
        <div class="mb-6 flex flex-wrap gap-4 items-center justify-between">
            <div class="flex gap-4">
                <a href="new_complaint.php" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">
                    File New Complaint
                </a>
                <a href="dashboard.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">
                    Back to Dashboard
                </a>
            </div>
            
            <form method="GET" class="flex gap-2">
                <input type="text" name="search" placeholder="Search my complaints..." 
                       value="<?php echo htmlspecialchars($search_term); ?>"
                       class="px-4 py-2 bg-gray-700 text-white border border-gray-600 rounded focus:border-red-500 focus:outline-none w-64">
                <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Search</button>
                <?php if (!empty($search_term)): ?>
                    <a href="my_complaints.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">Clear</a>
                <?php endif; ?>
            </form>
        </div>

        <!-- Complaints Table -->
        <div class="bg-gray-800 border-2 border-red-600 rounded-lg p-6">
            <h2 class="text-white text-xl font-bold mb-4">
                My Complaints 
                <?php if (!empty($search_term)): ?>
                    <span class="text-sm text-gray-400">(Search results for: "<?php echo htmlspecialchars($search_term); ?>")</span>
                <?php endif; ?>
            </h2>
            
            <div class="overflow-x-auto">
                <table class="w-full text-white text-sm">
                    <thead>
                        <tr class="border-b border-gray-600">
                            <th class="text-left py-3">Complaint #</th>
                            <th class="text-left py-3">Type</th>
                            <th class="text-left py-3">Status</th>
                            <th class="text-left py-3">Assigned Officer</th>
                            <th class="text-left py-3">Incident Date</th>
                            <th class="text-left py-3">Filed Date</th>
                            <th class="text-left py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($complaints)): ?>
                            <?php foreach ($complaints as $complaint): ?>
                                <tr class="border-b border-gray-700 hover:bg-gray-700">
                                    <td class="py-3 font-mono"><?php echo htmlspecialchars($complaint['complaint_number']); ?></td>
                                    <td class="py-3"><?php echo htmlspecialchars($complaint['complaint_type']); ?></td>
                                    <td class="py-3">
                                        <span class="px-2 py-1 rounded text-xs
                                            <?php 
                                            switch($complaint['status']) {
                                                case 'resolved': echo 'bg-green-600'; break;
                                                case 'attended': echo 'bg-blue-600'; break;
                                                case 'in_progress': echo 'bg-yellow-600'; break;
                                                case 'not_attended': echo 'bg-red-600'; break;
                                                default: echo 'bg-gray-600';
                                            }
                                            ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $complaint['status'])); ?>
                                        </span>
                                    </td>
                                    <td class="py-3">
                                        <?php 
                                        if ($complaint['first_name']) {
                                            echo htmlspecialchars($complaint['first_name'] . ' ' . $complaint['last_name']);
                                        } else {
                                            echo '<span class="text-gray-400">Not assigned</span>';
                                        }
                                        ?>
                                    </td>
                                    <td class="py-3"><?php echo date('M d, Y', strtotime($complaint['incident_date'])); ?></td>
                                    <td class="py-3"><?php echo date('M d, Y H:i', strtotime($complaint['created_at'])); ?></td>
                                    <td class="py-3">
                                        <a href="view_complaint.php?id=<?php echo $complaint['complaint_id']; ?>" 
                                           class="text-blue-400 hover:text-blue-300 text-xs">View Details</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-8 text-gray-400">
                                    <?php echo !empty($search_term) ? 'No complaints found matching your search.' : 'You have not filed any complaints yet.'; ?>
                                    <?php if (empty($search_term)): ?>
                                        <br><a href="new_complaint.php" class="text-blue-400 hover:text-blue-300 underline">File your first complaint</a>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Complaint Statistics -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-red-400 font-semibold">Not Attended</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($complaints, function($c) { return $c['status'] == 'not_attended'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-blue-400 font-semibold">Attended</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($complaints, function($c) { return $c['status'] == 'attended'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-yellow-400 font-semibold">In Progress</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($complaints, function($c) { return $c['status'] == 'in_progress'; })); ?>
                </p>
            </div>
            <div class="bg-gray-800 border border-red-600 rounded p-4 text-center">
                <h3 class="text-green-400 font-semibold">Resolved</h3>
                <p class="text-white text-2xl font-bold">
                    <?php echo count(array_filter($complaints, function($c) { return $c['status'] == 'resolved'; })); ?>
                </p>
            </div>
        </div>

        <!-- Information Box -->
        <div class="mt-8 bg-gray-800 border-2 border-red-600 rounded-lg p-6">
            <h3 class="text-white text-lg font-bold mb-4">Complaint Status Guide</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h4 class="text-red-400 font-semibold mb-2">Not Attended</h4>
                    <p class="text-gray-300">Your complaint has been received and is waiting for review by our officers.</p>
                </div>
                <div>
                    <h4 class="text-blue-400 font-semibold mb-2">Attended</h4>
                    <p class="text-gray-300">An officer has been assigned to your complaint and initial review has begun.</p>
                </div>
                <div>
                    <h4 class="text-yellow-400 font-semibold mb-2">In Progress</h4>
                    <p class="text-gray-300">Your complaint is being actively investigated by the assigned officer.</p>
                </div>
                <div>
                    <h4 class="text-green-400 font-semibold mb-2">Resolved</h4>
                    <p class="text-gray-300">Your complaint has been resolved. Check the details for the final outcome.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
