<?php
require_once '../config/database.php';
startSecureSession();
requireRole(['officer']);

$record = null;
$case_updates = [];
$error_message = '';

if (isset($_GET['id'])) {
    $record_id = (int)$_GET['id'];
    
    try {
        $pdo = getDBConnection();
        
        // Get criminal record details (only records created by this officer)
        $stmt = $pdo->prepare("SELECT cr.*, 
            u.first_name as created_by_fname, u.last_name as created_by_lname,
            ao.first_name as officer_fname, ao.last_name as officer_lname
            FROM criminal_records cr 
            LEFT JOIN users u ON cr.created_by = u.user_id 
            LEFT JOIN users ao ON cr.arresting_officer_id = ao.user_id
            WHERE cr.record_id = ? AND cr.created_by = ?");
        $stmt->execute([$record_id, $_SESSION['user_id']]);
        $record = $stmt->fetch();
        
        if ($record) {
            // Get case updates
            $stmt = $pdo->prepare("SELECT cu.*, u.first_name, u.last_name 
                FROM case_updates cu 
                LEFT JOIN users u ON cu.updated_by = u.user_id 
                WHERE cu.case_number = ? 
                ORDER BY cu.update_date DESC");
            $stmt->execute([$record['case_number']]);
            $case_updates = $stmt->fetchAll();
        }
        
    } catch (PDOException $e) {
        $error_message = "Failed to load record details.";
    }
} else {
    $error_message = "No record ID provided.";
}

if (!$record) {
    $error_message = "Record not found or you don't have permission to view it.";
}
?>

<!DOCTYPE html>
<html>
<head>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css?family=DM Sans" rel="stylesheet">
    <title>View Criminal Record - CRMS Officer</title>
    <style>
        .info-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border: 2px solid #dc2626;
            border-radius: 8px;
            padding: 20px;
        }
        @media print {
            .no-print { display: none; }
            body { background: white !important; }
            .bg-gray-800 { background: white !important; color: black !important; }
            .text-white { color: black !important; }
        }
    </style>
</head>
<body class="bg-gradient-to-l from-[#000000] via-[#121417] to-[#25282d] min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-900 border-b-2 border-red-600 p-4 no-print">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <img src="../assets/police-force(3).png" alt="LPF Logo" class="h-10 w-10">
                <h1 class="text-white text-xl font-bold">CRMS - View Criminal Record</h1>
            </div>
            <div class="flex items-center space-x-4">
                <a href="manage_records.php" class="text-white hover:text-gray-300">Back to My Cases</a>
                <a href="dashboard.php" class="text-white hover:text-gray-300">Dashboard</a>
                <span class="text-white">Officer <?php echo htmlspecialchars($_SESSION['user_name']); ?></span>
                <a href="../config/logout.php" class="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
        <?php if (!empty($error_message)): ?>
            <div class="bg-red-600 text-white p-4 rounded mb-6">
                <?php echo htmlspecialchars($error_message); ?>
                <div class="mt-4">
                    <a href="manage_records.php" class="underline">Return to My Cases</a>
                </div>
            </div>
        <?php else: ?>
            <!-- Action Buttons -->
            <div class="mb-6 flex gap-4 no-print">
                <a href="edit_record.php?id=<?php echo $record['record_id']; ?>" 
                   class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700">Edit Record</a>
                <button onclick="window.print()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Print Record</button>
                <a href="manage_records.php" class="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700">Back to My Cases</a>
            </div>

            <!-- Record Header -->
            <div class="info-card mb-6">
                <div class="text-center mb-6">
                    <h1 class="text-white text-3xl font-bold">Criminal Record Details</h1>
                    <p class="text-gray-300">Case Number: <span class="font-mono text-red-400"><?php echo htmlspecialchars($record['case_number']); ?></span></p>
                    <p class="text-gray-300">Generated on: <?php echo date('F d, Y H:i'); ?></p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Case Status -->
                    <div>
                        <h3 class="text-white text-lg font-semibold mb-2">Case Status</h3>
                        <span class="px-4 py-2 rounded text-sm font-semibold
                            <?php 
                            switch($record['case_status']) {
                                case 'solved': echo 'bg-green-600 text-white'; break;
                                case 'ongoing': echo 'bg-yellow-600 text-white'; break;
                                case 'paused': echo 'bg-blue-600 text-white'; break;
                                case 'unresolved': echo 'bg-red-600 text-white'; break;
                                default: echo 'bg-gray-600 text-white';
                            }
                            ?>">
                            <?php echo ucfirst($record['case_status']); ?>
                        </span>
                    </div>
                    
                    <!-- Dates -->
                    <div>
                        <h3 class="text-white text-lg font-semibold mb-2">Important Dates</h3>
                        <p class="text-gray-300">Incident Date: <span class="text-white"><?php echo date('F d, Y', strtotime($record['incident_date'])); ?></span></p>
                        <p class="text-gray-300">Record Created: <span class="text-white"><?php echo date('F d, Y H:i', strtotime($record['created_at'])); ?></span></p>
                        <p class="text-gray-300">Last Updated: <span class="text-white"><?php echo date('F d, Y H:i', strtotime($record['updated_at'])); ?></span></p>
                    </div>
                </div>
            </div>

            <!-- Suspect Information -->
            <div class="info-card mb-6">
                <h2 class="text-white text-xl font-bold mb-4">Suspect Information</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-gray-300">Name: <span class="text-white font-semibold"><?php echo htmlspecialchars($record['suspect_name']); ?></span></p>
                        <p class="text-gray-300">ID Number: <span class="text-white"><?php echo htmlspecialchars($record['suspect_id_number'] ?: 'Not provided'); ?></span></p>
                    </div>
                </div>
            </div>

            <!-- Crime Information -->
            <div class="info-card mb-6">
                <h2 class="text-white text-xl font-bold mb-4">Crime Information</h2>
                <div class="space-y-4">
                    <div>
                        <p class="text-gray-300">Crime Type: <span class="text-white font-semibold"><?php echo htmlspecialchars($record['crime_type']); ?></span></p>
                        <p class="text-gray-300">Location: <span class="text-white"><?php echo htmlspecialchars($record['incident_location']); ?></span></p>
                    </div>
                    <div>
                        <h3 class="text-white font-semibold mb-2">Description:</h3>
                        <p class="text-gray-300 bg-gray-700 p-3 rounded"><?php echo nl2br(htmlspecialchars($record['crime_description'])); ?></p>
                    </div>
                </div>
            </div>

            <!-- Evidence and Witnesses -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="info-card">
                    <h2 class="text-white text-xl font-bold mb-4">Evidence</h2>
                    <div class="text-gray-300 bg-gray-700 p-3 rounded min-h-[100px]">
                        <?php echo $record['evidence'] ? nl2br(htmlspecialchars($record['evidence'])) : 'No evidence recorded.'; ?>
                    </div>
                </div>
                
                <div class="info-card">
                    <h2 class="text-white text-xl font-bold mb-4">Witness Information</h2>
                    <div class="text-gray-300 bg-gray-700 p-3 rounded min-h-[100px]">
                        <?php echo $record['witness_info'] ? nl2br(htmlspecialchars($record['witness_info'])) : 'No witness information recorded.'; ?>
                    </div>
                </div>
            </div>

            <!-- Case Updates -->
            <?php if (!empty($case_updates)): ?>
                <div class="info-card mb-6">
                    <h2 class="text-white text-xl font-bold mb-4">Case Updates History</h2>
                    <div class="space-y-3">
                        <?php foreach ($case_updates as $update): ?>
                            <div class="bg-gray-700 p-3 rounded">
                                <div class="flex justify-between items-start mb-2">
                                    <span class="text-blue-400 font-semibold"><?php echo ucfirst(str_replace('_', ' ', $update['update_type'])); ?></span>
                                    <span class="text-gray-400 text-sm"><?php echo date('M d, Y H:i', strtotime($update['update_date'])); ?></span>
                                </div>
                                <p class="text-gray-300"><?php echo htmlspecialchars($update['update_description']); ?></p>
                                <p class="text-gray-400 text-sm mt-1">Updated by: <?php echo htmlspecialchars($update['first_name'] . ' ' . $update['last_name']); ?></p>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Court Information -->
            <?php if ($record['court_date'] || $record['verdict']): ?>
                <div class="info-card">
                    <h2 class="text-white text-xl font-bold mb-4">Court Information</h2>
                    <div class="space-y-2">
                        <?php if ($record['court_date']): ?>
                            <p class="text-gray-300">Court Date: <span class="text-white"><?php echo date('F d, Y', strtotime($record['court_date'])); ?></span></p>
                        <?php endif; ?>
                        <?php if ($record['verdict']): ?>
                            <div>
                                <h3 class="text-white font-semibold mb-2">Verdict:</h3>
                                <p class="text-gray-300 bg-gray-700 p-3 rounded"><?php echo nl2br(htmlspecialchars($record['verdict'])); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
